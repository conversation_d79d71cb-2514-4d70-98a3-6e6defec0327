import { useEffect } from "react"

export const useKeyboardShortcuts = (
  selectedNodeId: string | null,
  deleteNodeWithChildren: (nodeId: string) => void,
  addChildNode: (nodeId: string) => void,
  addSiblingNode: (nodeId: string) => void,
  addParentNode: (nodeId: string) => void
) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 如果正在编辑节点，不处理快捷键
      const activeElement = document.activeElement
      if (activeElement && (activeElement.tagName === "SPAN" && activeElement.getAttribute("contenteditable") === "true")) {
        return
      }

      // 删除节点 - Delete键
      if (e.key === "Delete" && selectedNodeId && selectedNodeId !== "root") {
        e.preventDefault()
        deleteNodeWithChildren(selectedNodeId)
        return
      }

      // 新增子主题 - Tab键
      if (e.key === "Tab" && !e.shiftKey && selectedNodeId) {
        e.preventDefault()
        addChildNode(selectedNodeId)
        return
      }

      // 新增同级主题 - Enter键
      if (e.key === "Enter" && !e.shiftKey && selectedNodeId && selectedNodeId !== "root") {
        e.preventDefault()
        addSiblingNode(selectedNodeId)
        return
      }

      // 新增父主题 - Shift + Tab键
      if (e.key === "Tab" && e.shiftKey && selectedNodeId && selectedNodeId !== "root") {
        e.preventDefault()
        addParentNode(selectedNodeId)
        return
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => {
      document.removeEventListener("keydown", handleKeyDown)
    }
  }, [selectedNodeId, deleteNodeWithChildren, addChildNode, addSiblingNode, addParentNode])
}
