import * as ToolbarPrimitive from "@radix-ui/react-toolbar"
import React from "react"
import { ToolbarColor } from "../../common/ToolbarColor"
import { ToolbarSelect } from "../../common/ToolbarSelect"
import { ToolbarSeparator } from "../../common/ToolbarSeparator"
import { TooltipButton } from "../../common/TooltipButton"
import type { ToolbarItem } from "../config/startToolbarConfig"
import type { ToolbarSectionProps } from "../type/types"

interface ToolbarRendererProps extends ToolbarSectionProps {
  className?: string
  config: ToolbarItem[]
  ariaLabel: string
  separatorClassName?: string
}

// 工具函数：安全地评估字符串表达式
const safeEval = (expression: string, context: any): any => {
  try {
    const cleanExpression = expression.replace(/\s+as\s+\w+/g, "")
    const func = new Function(
      ...Object.keys(context),
      `return ${cleanExpression}`
    )
    return func(...Object.values(context))
  } catch (error) {
    console.warn(`Failed to evaluate expression: ${expression}`, error)
    return false
  }
}

// 工具函数：获取方法引用
const getMethodRef = (
  methodName: string,
  context: Record<string, any>
): any => {
  const keys = methodName.split(".")
  let result = context

  for (const key of keys) {
    result = result?.[key]
    if (result === undefined) break
  }

  return typeof result === "function" ? result : undefined
}

export const ToolbarRenderer: React.FC<ToolbarRendererProps> = ({
  config,
  ariaLabel,
  separatorClassName,
  selectedNodeId,
  selectedNodeStyle,
  onAddChildNode,
  onAddSiblingNode,
  onAddParentNode,
  onToggleBold,
  onToggleItalic,
  onColorChange,
  onFontFamilyChange,
  onFontSizeChange,
  onTextAlignChange,
  onBorderWidthChange,
  onBackgroundColorChange,
  onScrollToCenter,
  onResetScroll,
  className
}) => {
  const isEnabled = selectedNodeId !== null

  const context = {
    isEnabled,
    selectedNodeId,
    selectedNodeStyle,
    onAddChildNode,
    onAddSiblingNode,
    onAddParentNode,
    onToggleBold,
    onToggleItalic,
    onColorChange,
    onFontFamilyChange,
    onFontSizeChange,
    onTextAlignChange,
    onBorderWidthChange,
    onBackgroundColorChange,
    onScrollToCenter,
    onResetScroll
  }

  const renderItem = (item: ToolbarItem, index: number): React.ReactNode => {
    switch (item.type) {
      case "separator":
        return (
          <ToolbarSeparator
            key={`separator-${index}`}
            className={separatorClassName || "toolbar-separator-vertical"}
          />
        )

      case "button": {
        const disabled =
          typeof item.disabled === "string"
            ? safeEval(item.disabled, context)
            : item.disabled || false

        const active = item.active ? safeEval(item.active, context) : false

        const handleClick = item.onClick
          ? () => {
              if (item.onClick?.startsWith("()")) {
                // 处理箭头函数形式的onClick
                safeEval(item.onClick, context)
              } else if (item.onClick) {
                const method = getMethodRef(item.onClick, context)
                method?.()
              }
            }
          : undefined

        // 检查 Icon 是否是有效的 React 组件类型
        // 支持 React.FC, React.ForwardRefExoticComponent, 和其他函数组件
        const IconComponent =
          item.icon &&
          (typeof item.icon === "function" ||
            (typeof item.icon === "object" &&
              item.icon !== null &&
              "render" in item.icon))
            ? (item.icon as React.ComponentType)
            : null

        return (
          <TooltipButton
            key={item.id || `button-${index}`}
            label={item.label || ""}
            onClick={handleClick}
            disabled={disabled}
            active={active}
            className={item.className}
          >
            {IconComponent && <IconComponent />}
            {item.text && <span>{item.text}</span>}
          </TooltipButton>
        )
      }

      case "select": {
        const disabled =
          typeof item.disabled === "string"
            ? safeEval(item.disabled, context)
            : item.disabled || false

        const value = item.value ? safeEval(item.value, context) : ""

        const handleChange = item.onChange
          ? (val: string) => {
              const method = getMethodRef(item.onChange!, context)

              if (item.id === "fontSize") {
                // 特殊处理字号，需要转换为数字
                method?.(parseInt(val, 10))
              } else {
                method?.(val)
              }
            }
          : () => {}

        return (
          <ToolbarSelect
            key={item.id || `select-${index}`}
            label={item.label || ""}
            options={item.options || []}
            value={value}
            onChange={handleChange}
            disabled={disabled}
            className={item.className}
          />
        )
      }

      case "color": {
        const disabled =
          typeof item.disabled === "string"
            ? safeEval(item.disabled, context)
            : item.disabled || false

        const value = item.value ? safeEval(item.value, context) : "#000000"

        console.log("🚀 ~~ value  🤖--EndLog--🤖", value)

        const handleChange = item.onChange
          ? (color: string) => {
              const method = getMethodRef(item.onChange!, context)
              method?.(color)
            }
          : () => {}

        return (
          <ToolbarColor
            key={item.id || `color-${index}`}
            label={item.label || ""}
            value={value}
            onChange={handleChange}
            disabled={disabled}
            icon={item.icon as React.ReactNode}
            showTitle={item.showTitle}
          />
        )
      }

      case "group":
        return (
          <div key={item.id || `group-${index}`} className={item.className}>
            {item.children?.map((child, childIndex) =>
              renderItem(child, childIndex)
            )}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <ToolbarPrimitive.Root
      className={`toolbar   ${className}`}
      aria-label={ariaLabel}
    >
      {config.map((item, index) => renderItem(item, index))}
    </ToolbarPrimitive.Root>
  )
}
