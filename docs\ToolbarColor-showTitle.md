# ToolbarColor 组件 showTitle 功能

## 概述

`ToolbarColor` 组件现在支持通过 `showTitle` 属性来控制是否显示鼠标悬停时的 title 提示。

## 属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `showTitle` | `boolean` | `true` | 控制是否显示鼠标悬停时的 title 提示 |

## 使用方法

### 1. 显示 Title（默认行为）

```tsx
<ToolbarColor
  label="字体颜色"
  value="#ff0000"
  onChange={handleColorChange}
  icon={<ColorIcon />}
  showTitle={true} // 可以省略，默认为 true
/>
```

### 2. 隐藏 Title

```tsx
<ToolbarColor
  label="背景颜色"
  value="#00ff00"
  onChange={handleColorChange}
  icon={<BackgroundIcon />}
  showTitle={false} // 明确设置为 false
/>
```

## 在工具栏配置中使用

### 样式工具栏配置示例

```typescript
// homework-3/client/src/components/Toolbar/config/styleToolbarConfig.ts
{
  type: "color",
  id: "nodeBackground",
  label: "节点背景",
  icon: StyleBackgroundIcon,
  value: "selectedNodeStyle?.backgroundColor || '#ffffff'",
  onChange: "onBackgroundColorChange",
  disabled: "!isEnabled",
  showTitle: false // 不显示 title
}
```

### 开始工具栏配置示例

```typescript
// homework-3/client/src/components/Toolbar/config/startToolbarConfig.ts
{
  type: "color",
  id: "fontColor",
  label: "字体颜色",
  icon: ColorIcon,
  value: "selectedNodeStyle?.color || '#000000'",
  onChange: "onColorChange",
  disabled: "!isEnabled",
  showTitle: true // 显示 title（默认值）
}
```

## 实现细节

1. **ToolbarColorProps 接口更新**：添加了可选的 `showTitle?: boolean` 属性
2. **ToolbarColor 组件更新**：根据 `showTitle` 属性决定是否传递 `title` 给子组件
3. **ToolbarItem 接口更新**：添加了 `showTitle?: boolean` 属性支持
4. **ToolbarRenderer 更新**：在渲染颜色选择器时传递 `showTitle` 属性

## 向后兼容性

此更新完全向后兼容。如果不指定 `showTitle` 属性，组件将默认显示 title，保持原有行为不变。

## 使用场景

- **显示 Title**：当用户需要明确知道颜色选择器的用途时
- **隐藏 Title**：当界面空间有限或者图标已经足够清晰表达用途时
- **一致性设计**：在同一个工具栏中，可以根据设计需求统一控制 title 的显示
