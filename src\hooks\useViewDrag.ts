import type { DragOffset, MindMapNode, ViewOffset } from "@/types/mindmap"
import React, { useCallback, useRef, useState } from "react"

export const useViewDrag = (mindMapNodes?: Record<string, MindMapNode>) => {
  const [isDragging, setIsDragging] = useState(false)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [dragOffset, setDragOffset] = useState<DragOffset>({ x: 0, y: 0 })
  const [viewOffset, setViewOffset] = useState<ViewOffset>({ x: 0, y: 0 })
  const [scrollAreaSize, setScrollAreaSize] = useState({
    width: 3000,
    height: 3000,
    offsetX: 0,
    offsetY: 0
  })

  // 容器引用，用于滚动控制
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // 用于跟踪鼠标按下状态和位置
  const mouseDownRef = useRef<{
    isDown: boolean
    startX: number
    startY: number
    timestamp: number
    initialScrollLeft: number
    initialScrollTop: number
  }>({
    isDown: false,
    startX: 0,
    startY: 0,
    timestamp: 0,
    initialScrollLeft: 0,
    initialScrollTop: 0
  })

  // 拖拽阈值（像素）
  const DRAG_THRESHOLD = 5
  // 双击检测时间窗口（毫秒）
  const DOUBLE_CLICK_WINDOW = 300

  // 计算思维导图的边界
  const calculateMindMapBounds = useCallback(() => {
    if (!mindMapNodes || Object.keys(mindMapNodes).length === 0) {
      return { minX: 0, maxX: 0, minY: 0, maxY: 0, width: 0, height: 0 }
    }

    const nodes = Object.values(mindMapNodes)
    let minX = Infinity,
      maxX = -Infinity,
      minY = Infinity,
      maxY = -Infinity

    nodes.forEach((node) => {
      // 估算节点的大小（基于文本长度和层级）
      const estimatedWidth = Math.max(100, node.text.length * 8 + 40)
      const estimatedHeight = 40

      const nodeMinX = node.x - estimatedWidth / 2
      const nodeMaxX = node.x + estimatedWidth / 2
      const nodeMinY = node.y - estimatedHeight / 2
      const nodeMaxY = node.y + estimatedHeight / 2

      minX = Math.min(minX, nodeMinX)
      maxX = Math.max(maxX, nodeMaxX)
      minY = Math.min(minY, nodeMinY)
      maxY = Math.max(maxY, nodeMaxY)
    })

    // 添加一些边距
    const padding = 200
    minX -= padding
    maxX += padding
    minY -= padding
    maxY += padding

    return {
      minX,
      maxX,
      minY,
      maxY,
      width: maxX - minX,
      height: maxY - minY
    }
  }, [mindMapNodes])

  // 更新滚动区域大小
  const updateScrollArea = useCallback(() => {
    const bounds = calculateMindMapBounds()
    const container = containerRef.current

    if (container && scrollAreaRef.current) {
      const containerWidth = container.clientWidth
      const containerHeight = container.clientHeight

      // 计算需要的偏移量，确保所有内容都在正坐标范围内
      const offsetX = Math.max(0, -bounds.minX)
      const offsetY = Math.max(0, -bounds.minY)

      // 确保滚动区域至少和容器一样大，并且能容纳整个思维导图
      const newWidth = Math.max(containerWidth, bounds.width + offsetX, 1000)
      const newHeight = Math.max(containerHeight, bounds.height + offsetY, 1000)

      setScrollAreaSize({
        width: newWidth,
        height: newHeight,
        offsetX,
        offsetY
      })

      // 设置滚动区域的样式
      scrollAreaRef.current.style.width = `${newWidth}px`
      scrollAreaRef.current.style.height = `${newHeight}px`
    }
  }, [calculateMindMapBounds])

  // 监听思维导图节点变化，更新滚动区域
  // 使用setTimeout来避免直接使用useEffect
  const scheduleScrollAreaUpdate = useCallback(() => {
    setTimeout(updateScrollArea, 100)
  }, [updateScrollArea])

  // 视图拖拽功能
  const handleNodeMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) {
      // 只响应左键
      e.stopPropagation()

      const now = Date.now()
      const timeSinceLastDown = now - mouseDownRef.current.timestamp

      // 如果在双击时间窗口内，不开始拖拽准备
      if (timeSinceLastDown < DOUBLE_CLICK_WINDOW) {
        mouseDownRef.current.isDown = false
        return
      }

      const container = containerRef.current
      if (!container) return

      // 记录鼠标按下状态和位置，包括当前滚动位置
      mouseDownRef.current = {
        isDown: true,
        startX: e.clientX,
        startY: e.clientY,
        timestamp: now,
        initialScrollLeft: container.scrollLeft,
        initialScrollTop: container.scrollTop
      }

      // 预设拖拽偏移，但不设置 isDragging
      setDragOffset({
        x: e.clientX - viewOffset.x,
        y: e.clientY - viewOffset.y
      })
    }
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (mouseDownRef.current.isDown && !isDragging) {
      // 检查是否移动了足够的距离来开始拖拽
      const deltaX = Math.abs(e.clientX - mouseDownRef.current.startX)
      const deltaY = Math.abs(e.clientY - mouseDownRef.current.startY)

      if (deltaX > DRAG_THRESHOLD || deltaY > DRAG_THRESHOLD) {
        // 开始拖拽
        setIsDragging(true)
      }
    }

    if (isDragging && containerRef.current) {
      // 使用滚动来实现拖拽效果
      const container = containerRef.current
      const deltaX = mouseDownRef.current.startX - e.clientX
      const deltaY = mouseDownRef.current.startY - e.clientY

      container.scrollLeft = mouseDownRef.current.initialScrollLeft + deltaX
      container.scrollTop = mouseDownRef.current.initialScrollTop + deltaY

      // 更新viewOffset以保持与滚动位置同步（用于其他组件的兼容性）
      setViewOffset({
        x: -container.scrollLeft,
        y: -container.scrollTop
      })
    }
  }

  const handleMouseUp = () => {
    mouseDownRef.current.isDown = false
    setIsDragging(false)
  }

  // 滚动到指定位置
  const scrollToPosition = useCallback((x: number, y: number) => {
    if (containerRef.current) {
      containerRef.current.scrollTo({
        left: x,
        top: y,
        behavior: "smooth"
      })
    }
  }, [])

  // 滚动到思维导图中心
  const scrollToCenter = useCallback(() => {
    if (containerRef.current && scrollAreaRef.current) {
      const container = containerRef.current
      const bounds = calculateMindMapBounds()

      // 计算偏移量
      const offsetX = Math.max(0, -bounds.minX)
      const offsetY = Math.max(0, -bounds.minY)

      // 计算思维导图的中心点（考虑偏移量）
      const mindMapCenterX = (bounds.minX + bounds.maxX) / 2 + offsetX
      const mindMapCenterY = (bounds.minY + bounds.maxY) / 2 + offsetY

      // 计算滚动位置，使思维导图中心显示在容器中心
      const scrollX = mindMapCenterX - container.clientWidth / 2
      const scrollY = mindMapCenterY - container.clientHeight / 2

      scrollToPosition(Math.max(0, scrollX), Math.max(0, scrollY))
    }
  }, [scrollToPosition, calculateMindMapBounds])

  // 重置滚动位置
  const resetScroll = useCallback(() => {
    scrollToPosition(0, 0)
  }, [scrollToPosition])

  // 跟踪是否已经进行过初始居中
  const [hasInitialCentered, setHasInitialCentered] = React.useState(false)

  // 更新滚动区域（当节点变化时）
  React.useEffect(() => {
    scheduleScrollAreaUpdate()
  }, [mindMapNodes, scheduleScrollAreaUpdate])

  // 只在初始加载时居中一次
  React.useEffect(() => {
    if (
      !hasInitialCentered &&
      mindMapNodes &&
      Object.keys(mindMapNodes).length > 0
    ) {
      setTimeout(() => {
        if (containerRef.current && scrollAreaRef.current) {
          const container = containerRef.current
          const bounds = calculateMindMapBounds()

          // 计算偏移量
          const offsetX = Math.max(0, -bounds.minX)
          const offsetY = Math.max(0, -bounds.minY)

          // 计算思维导图的中心点（考虑偏移量）
          const mindMapCenterX = (bounds.minX + bounds.maxX) / 2 + offsetX
          const mindMapCenterY = (bounds.minY + bounds.maxY) / 2 + offsetY

          // 计算滚动位置，使思维导图中心显示在容器中心
          const scrollX = mindMapCenterX - container.clientWidth / 2
          const scrollY = mindMapCenterY - container.clientHeight / 2

          container.scrollTo({
            left: Math.max(0, scrollX),
            top: Math.max(0, scrollY),
            behavior: "smooth"
          })

          // 标记已经进行过初始居中
          setHasInitialCentered(true)
        }
      }, 300) // 延迟一点确保DOM已渲染
    }
  }, [hasInitialCentered, mindMapNodes, calculateMindMapBounds])

  return {
    isDragging,
    viewOffset,
    containerRef,
    scrollAreaRef,
    scrollAreaSize,
    handleNodeMouseDown,
    handleMouseMove,
    handleMouseUp,
    scrollToPosition,
    scrollToCenter,
    resetScroll,
    updateScrollArea
  }
}
