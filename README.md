#### ColorPicker 组件

- **文件**: `src/components/ColorPicker.tsx`
- **依赖**: `@radix-ui/react-popover`, `react-color`
- **功能**:
  - 标签样式的颜色选择按钮 (A + 颜色下划线 + 下拉箭头)
  - 点击后弹出专业的 SketchPicker 颜色选择器
  - 预定义颜色调色板（80 种常用颜色）
  - HSV/RGB/Hex 颜色模式切换
  - 颜色滑块和色相环
  - 专业级颜色选择体验

# 思维导图动态节点

根据内容动态调整大小，同时限制最大内容为 100 个字符。节点大小会根据内容自动调整，保持思维导图的整体美观性和经典的中心连接方式。

### 1. 文本限制

- 限制节点内容最大为 100 个字符
- 实时字符计数显示，接近限制时变红提醒
- 自动截断超长文本，保持输入流畅性
- 支持多行文本显示和自动换行

### 2. 动态节点大小

- 使用 `div` + `span` 元素实现自然大小调整
- 节点宽度根据内容自动调整，最大宽度限制为 400px（根节点）/ 350px（子节点）
- 不同层级的节点有不同的最小宽度要求
- 支持 `contentEditable` 原地编辑体验

### 核心结构：div + span 组合

```typescript
// 节点容器使用 div，支持自然大小调整
<div
  ref={nodeRef}
  className={`mindmap-node level-${node.level}`}
  style={{
    minWidth: node.level === 0 ? "80px" : node.level === 1 ? "70px" : "60px",
    maxWidth: "500px",
    whiteSpace: "pre-wrap",
    wordWrap: "break-word",
    overflow: "visible",
    height: "auto",
    // 其他样式...
  }}
>
  {isEditing ? (
    // 编辑模式：使用 contentEditable span
    <span
      ref={editableRef}
      contentEditable
      suppressContentEditableWarning
      onInput={handleInput}
      onKeyDown={handleKeyDown}
      onBlur={onSaveEdit}
    >
      {editingText}
    </span>
  ) : (
    // 显示模式：普通 span
    <span>{node.text}</span>
  )}
</div>
```

- **自然大小调整**：div 元素会根据内容自动调整大小
- **内联编辑**：contentEditable span 提供原地编辑体验
- **固定距离布局**：节点中心之间保持 200px 固定距离，简单可靠
- **边缘连线**：连线从节点边缘连接，根据节点宽度动态计算连接点
- **成熟策略**：采用 React Flow 等成熟项目验证的布局算法
- **字符限制**：100 字符限制，保持思维导图简洁性
- **实时反馈**：字符计数显示，接近限制时变红提醒

## 特性

- ✅ 100 字符内容限制，防止节点过大
- ✅ 实时字符计数显示
- ✅ 支持单行和多行文本
- ✅ 根据内容动态调整宽度
- ✅ 保持经典的中心连接方式
- ✅ 智能最小/最大宽度限制
- ✅ 不同层级节点差异化处理
- ✅ 防重叠的智能布局算法
- ✅ 支持所有字体样式属性

# Redux 状态管理

### 全局状态结构

```typescript
interface RootState {
  mindMap: MindMapState // 思维导图核心数据
  ui: UIState // UI 界面状态
  editor: EditorState // 编辑器状态
}
```

### 状态模块

#### 1. MindMap 模块 (`mindMapSlice.ts`)

管理思维导图的核心数据和操作：

```typescript
interface MindMapState {
  nodes: Record<string, MindMapNode> // 所有节点数据
  selectedNodeId: string | null // 当前选中的节点ID
}
```

**主要 Actions:**

- `addChildNode` - 添加子节点
- `addSiblingNode` - 添加同级节点
- `addParentNode` - 添加父节点
- `deleteNode` - 删除节点
- `deleteNodeWithChildren` - 删除节点及其子节点
- `updateNode` - 更新节点内容和样式
- `setSelectedNode` - 设置选中节点
- `resetMindMap` - 重置思维导图
- `importMindMap` - 导入思维导图

#### 2. UI 模块 (`uiSlice.ts`)

管理界面相关的状态：

```typescript
interface UIState {
  activeTab: string // 当前激活的标签页
  showAddButton: string | null // 显示添加按钮的节点ID
}
```

**主要 Actions:**

- `setActiveTab` - 设置激活标签页
- `setShowAddButton` - 设置显示添加按钮

#### 3. Editor 模块 (`editorSlice.ts`)

管理节点编辑相关的状态：

```typescript
interface EditorState {
  isEditingNode: string | null // 当前编辑的节点ID
  editingText: string // 编辑中的文本
  showStylePanel: string | null // 显示样式面板的节点ID
  editingStyle: NodeStyle // 编辑中的样式
}
```

**主要 Actions:**

- `startEditNode` - 开始编辑节点
- `setEditingText` - 设置编辑文本
- `setEditingStyle` - 设置编辑样式
- `setShowStylePanel` - 设置样式面板显示
- `saveNodeEdit` - 保存节点编辑
- `cancelNodeEdit` - 取消节点编辑

### 1. 在组件中使用

```
typescript
import { useAppSelector, useAppDispatch } from '../store/hooks'
import { setSelectedNode, addChildNode } from '../store'
  const dispatch = useAppDispatch()
  const selectedNodeId = useAppSelector(state => state.mindMap.selectedNodeId)
  const nodes = useAppSelector(state => state.mindMap.nodes)

  const handleSelectNode = (nodeId: string) => {
    dispatch(setSelectedNode(nodeId))
  }

  const handleAddChild = (parentId: string) => {
    dispatch(addChildNode(parentId))
  }
```

### 2. 使用封装的 Hooksimport from '../hooks'

```typescript
function MindMapPage() {
  // 思维导图操作
  const {
    mindMapNodes,
    selectedNodeId,
    setSelectedNodeId,
    addChildNode,
    updateNode
  } = useMindMap()

  // 节点编辑
  const {
    isEditingNode,
    editingText,
    startEditNode,
    saveNodeEdit
  } = useNodeEditor()

  // UI 状态
  const {
    activeTab,
    setActiveTab,
    showAddButton,
    setShowAddButton
  } = useUI()

  return (

  )
}
```

### 3. 自动持久化

思维导图数据会自动保存到 localStorage：

```typescript
// 在 mindMapSlice 中自动触发
saveMindMapToStorage(state.nodes)
```

### 4. 位置计算

添加节点时自动重新计算所有节点位置：

```typescript
// 重新计算所有节点位置
state.nodes = recalculateAllPositions(state.nodes)
```

使用 React.memo 和 useCallback 优化组件渲染。

将大型状态分解为多个小的 slice，减少不必要的重新渲染。

### 添加中间件

```typescript
// 在 store/index.ts 中
 middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略action types的序列化检查
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
```

# 思维导图重置

清除 localStorage 中存储的所有思维导图数据并重置为默认状态。

## 使用方法

### 使用 Header 中的重置按钮

1. 在页面顶部的 Header 区域，您会看到一个新的"重置"按钮（带有勾选图标）
2. 点击该按钮
3. 系统会弹出确认对话框："确定要重置思维导图吗？这将清除所有数据并无法恢复。"
4. 点击"确定"即可重置思维导图

使用了现有的 `resetMindMap` action：

```typescript
resetMindMap: (state) => {
  const defaultNodes = {
    root: {
      id: "root",
      text: "未命名文件(1)",
      x: 500,
      y: 300,
      level: 0,
      children: [],
      style: getDefaultNodeStyle(),
    },
  }
  state.nodes = defaultNodes
  state.selectedNodeId = null
  // 清除localStorage
  clearMindMapStorage()
}
```

### Header 组件集成

- 在 `hooks.ts` 中添加了 `handleReset` 函数
- 在 `constants.ts` 中添加了重置按钮配置
- 在 `FileControls.tsx` 中集成了重置按钮

重置后，思维导图将恢复到初始状态：

- 只有一个根节点："未命名文件(1)"
- 位置在画布中心 (500, 300)
- 清除所有子节点
- 清除 localStorage 中的数据
- 重置选中状态

## 适用于：

- 开发和测试过程中快速清除测试数据
- 演示时重置到干净状态
- 解决 localStorage 数据损坏问题
- 快速开始新的思维导图创作
